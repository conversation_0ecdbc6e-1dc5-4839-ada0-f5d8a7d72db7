from typing import List
from ai_utility_orchestrator.core.tools import Tool
import logging

logger = logging.getLogger(__name__)

class ToolRegistry:
    """Registry for maintaining available tools with dynamic capabilities."""
    def __init__(self, config=None):
        self._tools = {}  # Dictionary to store tools by name
        self.config = config or {}
        self.registration_callbacks = []  # For dynamic registration events

    def register_tool(self, tool: Tool):
        """Register a new tool with configurable logging."""
        self._tools[tool.name] = tool

        # Configurable registration message
        reg_template = self.config.get("registration_message", "Registered tool: {tool_name}")
        logger.info(reg_template.format(tool_name=tool.name))

        # Execute registration callbacks
        for callback in self.registration_callbacks:
            try:
                callback(tool)
            except Exception as e:
                logger.warning(f"Registration callback failed: {e}")

    def add_registration_callback(self, callback):
        """Add a callback to be executed when tools are registered."""
        self.registration_callbacks.append(callback)

    def get_tool(self, name: str) -> Tool:
        """Get a tool by name."""
        return self._tools.get(name)

    def get_tools(self) -> List[Tool]:
        """Get all registered tools."""
        return list(self._tools.values())

    def list_tools(self) -> List[str]:
        """Get list of registered tool names."""
        return list(self._tools.keys())

    def get_tools_by_category(self, category: str) -> List[Tool]:
        """Get tools filtered by category (if tools have category metadata)."""
        return [tool for tool in self._tools.values()
                if hasattr(tool, 'category') and tool.category == category]

    def search_tools(self, query: str) -> List[Tool]:
        """Search tools by name or description."""
        query_lower = query.lower()
        return [tool for tool in self._tools.values()
                if query_lower in tool.name.lower() or query_lower in tool.description.lower()]
