# ai_utility_orchestrator/core/agent_builder.py

import logging
import json
import re
from typing import Dict, Any, Optional
from ai_utility_orchestrator.utils.response_formatter import format_response
from ai_utility_orchestrator.utils.toolkit import ConfigUtils
from ai_utility_orchestrator.core.agent_registry import ToolRegistry
from ai_utility_orchestrator.core.tools import Tool
from ai_utility_orchestrator.utils.context_manager import ContextManager

# Setup logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def _build_tool_selection_prompt(user_input: str, tools: list, context_messages: list = None, config: dict = None) -> str:
    """Build an AI-powered, context-aware prompt for tool selection."""

    if not tools:
        return f"""
Analyze this user request and provide a helpful direct response since no tools are available:

User request: {user_input}

Respond with JSON:
{{"selected_tool": "none", "parameters": {{}}, "reasoning": "No tools available", "confidence": 1.0, "direct_response": "your_helpful_response"}}
"""

    # Build simple, universal tool descriptions
    tool_descriptions = []
    for tool in tools:
        desc = f"• {tool.name}: {tool.description}"
        if hasattr(tool, 'schema') and tool.schema and tool.schema.get('properties'):
            params = list(tool.schema['properties'].keys())
            if params:
                desc += f" (Parameters: {', '.join(params)})"
        tool_descriptions.append(desc)

    tools_text = "\n".join(tool_descriptions)

    # Add minimal context if available
    context_text = ""
    if context_messages and len(context_messages) > 0:
        context_text = f"\n\nRecent context available."

    # Universal, AI-powered prompt
    prompt = f"""You are an intelligent AI orchestrator. Analyze the user's request and determine the best approach.

Available tools:
{tools_text}

User request: {user_input}{context_text}

Instructions:
- If a tool can help, select it and provide appropriate parameters
- If no tool is needed, provide a direct helpful response
- Be intelligent about parameter selection
- Consider the user's actual intent

Respond with JSON only:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "brief_explanation", "confidence": 0.95, "direct_response": "response_if_no_tool_needed"}}
"""

    return prompt


def _enhance_tool_parameters(params: Dict[str, Any], tool: 'Tool', config: dict) -> Dict[str, Any]:
    """Allow AI to enhance tool parameters based on context."""
    try:
        enhancement_prompt = f"""
You are helping to optimize tool parameters.

Tool: {tool.name}
Description: {tool.description}
Current parameters: {json.dumps(params, indent=2)}
Original user query: {params.get('original_query', 'N/A')}

Based on the user's query and tool capabilities, suggest any parameter enhancements or additions that would improve the tool's effectiveness. Return the enhanced parameters as JSON.

Only modify parameters that would genuinely improve the result. If no enhancements are needed, return the original parameters.
"""

        # Configurable temperature for parameter enhancement
        enhancement_temp = config.get("parameter_enhancement", {}).get("temperature", 0.3)

        result = format_response(
            prompt=enhancement_prompt,
            formatter="json",
            model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
            temperature=enhancement_temp,
            return_meta=True
        )

        if result and not result.get("error"):
            enhanced = result.get("parsed_response", params)
            if isinstance(enhanced, dict):
                logger.info(f"🔧 Enhanced tool parameters: {enhanced}")
                return enhanced
    except Exception as e:
        logger.warning(f"Parameter enhancement failed: {e}")

    return params


def _ai_error_recovery(error: str, tool_name: str, user_input: str, registry, config: dict) -> str:
    """AI-powered error recovery with intelligent fallback suggestions."""
    if not config.get("enable_ai_error_recovery", False):
        return f"Error occurred with {tool_name}: {error}"

    try:
        # Get alternative tools
        available_tools = registry.get_tools()
        alternatives = [t.name for t in available_tools if t.name != tool_name]

        # Use AI to suggest recovery
        if config.get("prompt_templates", {}).get("error_recovery"):
            recovery_prompt = config["prompt_templates"]["error_recovery"].format(
                user_input=user_input,
                tool_name=tool_name,
                error=error,
                alternatives=", ".join(alternatives[:3])  # Top 3 alternatives
            )

            # Import already available at module level
            recovery_response = format_response(
                prompt=recovery_prompt,
                formatter="answer",
                model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
                temperature=0.3  # Lower temperature for error recovery
            )

            if recovery_response:
                return recovery_response

    except Exception as recovery_error:
        logger.warning(f"AI error recovery failed: {recovery_error}")

    # Fallback to intelligent default
    return f"I encountered an issue with the {tool_name} tool ({error}), but I can help you in other ways. Let me try a different approach or provide direct assistance."


# Removed old hardcoded discovery function - now using universal discovery


def _get_minimal_config() -> Dict[str, Any]:
    """Get minimal configuration for universal operation."""
    return {
        "ai_powered": True,
        "universal": True,
        "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
        "intelligence": {"adaptive_responses": True},
        "discovery": {"enabled": True, "auto_detect": True},
        "behavior": {"universal_compatibility": True}
    }

def _get_or_generate_system_prompt(config: Dict[str, Any], llm_client=None) -> str:
    """Get system prompt from config or generate one with AI."""
    if "system_prompt" in config:
        return config["system_prompt"]

    if llm_client and config.get("intelligence", {}).get("auto_generate_prompts", True):
        try:
            prompt = "Generate a concise system prompt for an AI orchestrator that routes user queries to appropriate tools. Be professional and helpful."
            response = _call_llm_safe(llm_client, prompt, config)
            if response:
                return response.strip()
        except:
            pass

    return "You are an intelligent AI orchestrator that helps users by routing queries to appropriate tools."

def _register_dynamic_tools(registry: 'ToolRegistry', tools: list, llm_client=None):
    """Register tools dynamically with AI-enhanced metadata."""
    for tool in tools:
        if callable(tool):
            # Convert function to tool
            name = getattr(tool, '__name__', 'unknown_tool')
            description = _generate_tool_description(tool, llm_client)
            schema = _generate_tool_schema(tool, llm_client)

            tool_obj = Tool(
                name=name,
                description=description,
                execute_func=tool,
                schema=schema,
                category="dynamic",
                ai_enhanced=True
            )
            registry.register_tool(tool_obj)
        elif isinstance(tool, dict):
            # Tool definition dictionary
            _register_tool_from_dict(registry, tool)

def _auto_discover_tools(registry: 'ToolRegistry', config: Dict[str, Any], llm_client=None):
    """Auto-discover tools from various sources."""
    if not config.get("discovery", {}).get("enabled", True):
        return

    # Discover from current namespace
    import sys
    current_frame = sys._getframe(1)
    if current_frame and current_frame.f_globals:
        _discover_from_namespace(registry, current_frame.f_globals, llm_client)

def _discover_from_namespace(registry: 'ToolRegistry', namespace: dict, llm_client=None):
    """Discover tools from a namespace."""
    for name, obj in namespace.items():
        if (callable(obj) and
            not name.startswith('_') and
            not name in ['agent_executor'] and
            _is_tool_candidate(obj)):

            description = _generate_tool_description(obj, llm_client)
            schema = _generate_tool_schema(obj, llm_client)

            tool_obj = Tool(
                name=name,
                description=description,
                execute_func=obj,
                schema=schema,
                category="discovered",
                ai_enhanced=True
            )
            registry.register_tool(tool_obj)

def _is_tool_candidate(func) -> bool:
    """Determine if a function is a good tool candidate."""
    if not callable(func):
        return False

    # Skip built-ins and system functions
    if hasattr(func, '__module__') and func.__module__ in ['builtins', 'sys']:
        return False

    # Must have parameters or docstring
    import inspect
    try:
        sig = inspect.signature(func)
        return len(sig.parameters) > 0 or bool(func.__doc__)
    except:
        return False

def _generate_tool_description(func, llm_client=None) -> str:
    """Generate tool description with AI or fallback."""
    if func.__doc__:
        return func.__doc__.strip().split('\n')[0]

    if llm_client:
        try:
            import inspect
            sig = inspect.signature(func)
            prompt = f"Generate a brief description for a tool function named '{func.__name__}' with signature {sig}. One sentence only."
            response = _call_llm_safe(llm_client, prompt, {})
            if response:
                return response.strip()
        except:
            pass

    return f"Tool: {getattr(func, '__name__', 'unknown')}"

def _generate_tool_schema(func, llm_client=None) -> Dict[str, Any]:
    """Generate tool schema with AI or fallback."""
    try:
        import inspect
        sig = inspect.signature(func)
        properties = {}
        required = []

        for param_name, param in sig.parameters.items():
            properties[param_name] = {
                "type": "string",
                "description": f"Parameter: {param_name}"
            }
            if param.default == inspect.Parameter.empty:
                required.append(param_name)

        return {
            "type": "object",
            "properties": properties,
            "required": required
        }
    except:
        return {}

def _register_tool_from_dict(registry: 'ToolRegistry', tool_dict: dict):
    """Register tool from dictionary definition."""
    try:
        if "execute_func" in tool_dict:
            # Import function from path
            execute_path = tool_dict["execute_func"]
            if "." in execute_path:
                module_path, func_name = execute_path.rsplit(".", 1)
                mod = __import__(module_path, fromlist=[func_name])
                execute_func = getattr(mod, func_name)
            else:
                # Assume it's in current namespace
                execute_func = globals().get(execute_path)
        else:
            execute_func = tool_dict.get("function")

        if execute_func:
            tool = Tool(
                name=tool_dict.get("name", "unknown"),
                description=tool_dict.get("description", ""),
                execute_func=execute_func,
                schema=tool_dict.get("schema", {}),
                category=tool_dict.get("category", "general"),
                ai_enhanced=tool_dict.get("ai_enhanced", True)
            )
            registry.register_tool(tool)
    except Exception as e:
        logger.debug(f"Could not register tool from dict: {e}")

def _call_llm_safe(llm_client, prompt: str, config: Dict[str, Any]) -> Optional[str]:
    """Safely call LLM with error handling."""
    if not llm_client:
        return None

    try:
        if hasattr(llm_client, 'chat'):
            # OpenAI-style client
            response = llm_client.chat.completions.create(
                model=config.get("llm", {}).get("model", "gpt-4o-mini"),
                messages=[{"role": "user", "content": prompt}],
                temperature=config.get("llm", {}).get("temperature", 0.7)
            )
            return response.choices[0].message.content
        elif callable(llm_client):
            # Function-style client
            return llm_client(prompt)
    except Exception as e:
        logger.debug(f"LLM call failed: {e}")

    return None

def _register_example_tools(registry: 'ToolRegistry'):
    """Register example tools to demonstrate universal capabilities."""
    from ai_utility_orchestrator.core.tools import EXAMPLE_TOOLS

    for tool_func in EXAMPLE_TOOLS:
        try:
            tool_obj = Tool(
                name=tool_func.__name__.replace('example_', ''),
                description=tool_func.__doc__ or f"Universal tool: {tool_func.__name__}",
                execute_func=tool_func,
                schema={},
                category="example",
                ai_enhanced=True
            )
            registry.register_tool(tool_obj)
        except Exception as e:
            logger.debug(f"Could not register example tool {tool_func.__name__}: {e}")

def _extract_tool_decision(llm_response: str, config: dict = None) -> Dict[str, Any]:
    """Extract tool selection decision from LLM response with robust error handling."""
    logger.info(f"🔍 Processing LLM response: {llm_response[:200]}...")

    if not llm_response or not llm_response.strip():
        logger.warning("Empty LLM response received")
        return {
            "selected_tool": "none",
            "parameters": {},
            "reasoning": "Empty response from LLM",
            "direct_response": "I didn't receive a proper response. Please try again."
        }

    cleaned_response = llm_response.strip()

    # Get configurable JSON extraction patterns
    extraction_config = config.get("json_extraction", {}) if config else {}
    patterns = extraction_config.get("patterns", [
        r'\{.*?\}',  # Default: any JSON object
        r'```json\s*(\{.*?\})\s*```',  # JSON in code blocks
        r'\{[^{}]*"selected_tool"[^{}]*\}',  # Tool selection specific
    ])

    # Build dynamic extraction methods based on config
    json_extraction_methods = []

    # Method 1: Direct JSON parsing (always first)
    json_extraction_methods.append(
        lambda text: json.loads(text) if text.startswith('{') and text.endswith('}') else None
    )

    # Dynamic methods based on patterns
    for pattern in patterns:
        if 'group(1)' in pattern or '```' in pattern:
            # Pattern with capture group
            json_extraction_methods.append(
                lambda text, p=pattern: json.loads(re.search(p, text, re.DOTALL).group(1)) if re.search(p, text, re.DOTALL) else None
            )
        else:
            # Pattern without capture group
            json_extraction_methods.append(
                lambda text, p=pattern: json.loads(re.search(p, text, re.DOTALL).group()) if re.search(p, text, re.DOTALL) else None
            )

    # Try each extraction method
    for i, method in enumerate(json_extraction_methods):
        try:
            result = method(cleaned_response)
            if result and isinstance(result, dict) and "selected_tool" in result:
                logger.info(f"JSON extracted using method {i+1}: {result}")
                return result
        except (json.JSONDecodeError, AttributeError, TypeError) as e:
            logger.debug(f"Method {i+1} failed: {e}")
            continue

    # If all JSON extraction fails, try to parse manually
    logger.warning("All JSON extraction methods failed, attempting manual parsing")

    # Look for key-value patterns
    tool_match = re.search(r'"selected_tool"\s*:\s*"([^"]*)"', cleaned_response)
    params_match = re.search(r'"parameters"\s*:\s*(\{[^}]*\})', cleaned_response)
    reasoning_match = re.search(r'"reasoning"\s*:\s*"([^"]*)"', cleaned_response)
    direct_match = re.search(r'"direct_response"\s*:\s*"([^"]*)"', cleaned_response)

    if tool_match:
        logger.info("Manual parsing successful")
        direct_resp = direct_match.group(1) if direct_match else cleaned_response
        return {
            "selected_tool": tool_match.group(1),
            "parameters": json.loads(params_match.group(1)) if params_match else {},
            "reasoning": reasoning_match.group(1) if reasoning_match else "Manual parsing",
            "direct_response": str(direct_resp) if direct_resp is not None else "I couldn't process your request."
        }

    # Final fallback - treat as direct response
    logger.warning(f"Could not parse response, treating as direct answer: {cleaned_response[:100]}...")
    return {
        "selected_tool": "none",
        "parameters": {},
        "reasoning": "Could not parse LLM response format",
        "direct_response": str(cleaned_response) if cleaned_response is not None else "I couldn't process your request."
    }


def agent_executor(user_input: str, config=None, user_id: str = None,
                  tools=None, llm_client=None, context_manager=None):
    """
    Universal AI-powered orchestration function.
    Completely dynamic and adaptable to any project.

    Args:
        user_input: The user's query
        config: Configuration dictionary (optional, minimal defaults used)
        user_id: User identifier for context management
        tools: List of tools/functions to register (optional)
        llm_client: LLM client for AI operations (optional)
        context_manager: Context manager instance (optional)
    """
    logger.info("🚀 Starting universal AI orchestrator...")

    # Use minimal config if not provided
    if config is None:
        config = _get_minimal_config()

    # Extract LLM configuration with adaptive defaults
    model_name = config.get("llm", {}).get("model", "gpt-4o-mini")
    temperature = config.get("llm", {}).get("temperature", 0.7)

    # AI-generate system prompt if not provided
    system_prompt = _get_or_generate_system_prompt(config, llm_client)

    # Use provided user_id or generate one
    if user_id is None:
        user_id = f"user_{hash(user_input) % 10000}"

    # Initialize or use provided context manager
    if context_manager is None:
        context_manager = ContextManager()

    # Setup universal tool registry
    registry = ToolRegistry()

    # Register provided tools dynamically
    if tools:
        _register_dynamic_tools(registry, tools, llm_client)

    # Auto-discover tools if enabled
    if config.get("discovery", {}).get("enabled", True):
        _auto_discover_tools(registry, config, llm_client)

    # If no tools were registered and no tools provided, register examples
    if not tools and len(registry.get_tools()) == 0:
        _register_example_tools(registry)

    try:
        # Get conversation context with configurable limit and format
        context_limit = config.get("context_limit", 3)
        context_format_config = config.get("context_format", {})
        context_messages = context_manager.get_recent_messages(user_id, limit=context_limit, format_config=context_format_config)

        # Set runtime context for this execution session
        context_manager.set_context("current_user_id", user_id)
        context_manager.set_context("current_query", user_input)
        context_manager.set_context("execution_timestamp", __import__('time').time())

        # Step 1: Ask LLM to select appropriate tool
        available_tools = registry.get_tools()
        tool_selection_prompt = _build_tool_selection_prompt(user_input, available_tools, context_messages, config)

        logger.info(" Asking LLM to select tool...")
        tool_decision_result = format_response(
            prompt=tool_selection_prompt,
            formatter="json",
            model_name=model_name,
            temperature=temperature,
            return_meta=True,
            system_prompt=system_prompt,
            messages=context_messages
        )

        # Step 2: Parse the tool decision with robust error handling
        if not tool_decision_result:
            logger.error("No response from LLM for tool selection")
            tool_decision = {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": "No response from LLM",
                "direct_response": "I'm having trouble processing your request. Please try again."
            }
        elif tool_decision_result.get("error"):
            logger.error(f"LLM API error: {tool_decision_result.get('error')}")
            tool_decision = {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": f"LLM API error: {tool_decision_result.get('error')}",
                "direct_response": "I'm experiencing technical difficulties. Please try again."
            }
        else:
            raw_decision = tool_decision_result.get("raw_response", "")
            parsed_decision = tool_decision_result.get("parsed_response", "")
            logger.info(f"LLM raw response: {raw_decision}")
            logger.info(f"🔧 Using model: {model_name}")

            # If parsed_response is already a dict, use it; otherwise extract from raw
            if isinstance(parsed_decision, dict) and "selected_tool" in parsed_decision:
                logger.info("Using pre-parsed JSON response")
                tool_decision = parsed_decision
                # Ensure direct_response is a string, not an object
                if "direct_response" in tool_decision and not isinstance(tool_decision["direct_response"], str):
                    tool_decision["direct_response"] = str(tool_decision["direct_response"])
            else:
                logger.info("Extracting tool decision from raw response")
                # Use raw response if parsed is not a proper dict
                decision_text = raw_decision if isinstance(parsed_decision, str) else str(parsed_decision)
                tool_decision = _extract_tool_decision(decision_text, config)

        selected_tool_name = tool_decision.get("selected_tool", "none")
        tool_parameters = tool_decision.get("parameters", {})
        reasoning = tool_decision.get("reasoning", "No reasoning provided")

        logger.info(f" LLM selected tool: {selected_tool_name} | Reasoning: {reasoning}")

        final_response = ""
        tool_result = None

        # Step 3: Execute the selected tool or provide direct response
        if selected_tool_name != "none":
            selected_tool = registry.get_tool(selected_tool_name)
            if selected_tool:
                logger.info(f"Executing tool: {selected_tool_name} with parameters: {tool_parameters}")
                try:
                    # Add registry and context manager to tool parameters for dynamic access
                    enhanced_params = tool_parameters.copy()
                    enhanced_params["registry"] = registry
                    enhanced_params["config"] = config
                    enhanced_params["user_id"] = user_id
                    enhanced_params["original_query"] = user_input
                    enhanced_params["context_manager"] = context_manager  # Add context manager access

                    # Allow AI to enhance parameters if configured
                    if config.get("enable_parameter_enhancement", False):
                        enhanced_params = _enhance_tool_parameters(enhanced_params, selected_tool, config)

                    tool_result = selected_tool.execute(enhanced_params)
                    logger.info(f" Tool execution successful: {tool_result}")

                    # Generate final response based on tool result
                    if config and "prompt_templates" in config and "final_response" in config["prompt_templates"]:
                        final_prompt = config["prompt_templates"]["final_response"].format(
                            user_input=user_input,
                            selected_tool_name=selected_tool_name,
                            tool_result=tool_result
                        )
                    else:
                        final_prompt = f"""Based on the tool execution result, provide a helpful response to the user.

User's original question: {user_input}
Tool used: {selected_tool_name}
Tool result: {tool_result}

Please provide a natural, helpful response to the user based on this information."""

                    final_result = format_response(
                        prompt=final_prompt,
                        formatter="answer",
                        model_name=model_name,
                        temperature=temperature,
                        return_meta=True,
                        system_prompt=system_prompt
                    )

                    final_response = final_result.get("parsed_response", str(tool_result))

                except Exception as e:
                    logger.error(f" Tool execution failed: {e}")
                    # Use AI-powered error recovery if enabled
                    if config.get("enable_ai_error_recovery", False):
                        final_response = _ai_error_recovery(str(e), selected_tool_name, user_input, registry, config)
                    else:
                        error_template = config.get("error_messages", {}).get("tool_execution_failed",
                            "I tried to use the {tool_name} tool, but encountered an error: {error}")
                        final_response = error_template.format(tool_name=selected_tool_name, error=str(e))
            else:
                logger.warning(f" Selected tool '{selected_tool_name}' not found in registry")
                not_found_template = config.get("error_messages", {}).get("tool_not_found",
                    "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}")
                fallback_response = tool_decision.get('direct_response', 'I apologize, but I cannot complete this request.')
                final_response = not_found_template.format(tool_name=selected_tool_name, fallback_response=fallback_response)
        else:
            # No tool needed, use direct response
            default_direct = config.get("default_responses", {}).get("no_tool_needed",
                "I can help you with that, but I'm not sure how to respond.")
            final_response = tool_decision.get("direct_response", default_direct)
            logger.info(" Providing direct response (no tool needed)")

        # Step 4: Save interaction to context
        context_manager.add_interaction(user_id, user_input, final_response)

        return {
            "input": user_input,
            "selected_tool": selected_tool_name,
            "tool_parameters": tool_parameters,
            "tool_result": tool_result,
            "reasoning": reasoning,
            "final_response": final_response,
            "used_model": model_name,
            "user_id": user_id
        }

    except Exception as e:
        logger.error(f" Agent execution failed: {e}")
        general_error_template = config.get("error_messages", {}).get("general_error",
            "I apologize, but I encountered an error while processing your request: {error}")
        error_response = general_error_template.format(error=str(e))

        # Still save the interaction even if there was an error
        try:
            context_manager.add_interaction(user_id, user_input, error_response)
        except:
            pass  # Don't let context saving errors break the main flow

        return {
            "input": user_input,
            "selected_tool": "none",
            "tool_parameters": {},
            "tool_result": None,
            "reasoning": "Error occurred during processing",
            "final_response": error_response,
            "used_model": model_name,
            "user_id": user_id,
            "error": str(e)
        }


