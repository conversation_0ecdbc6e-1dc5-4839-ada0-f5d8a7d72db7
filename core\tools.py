"""
Universal Tool System
Dynamic, AI-powered tool interface that can adapt any function into a tool.
"""

from typing import Callable, Dict, Any, Optional
import json
import logging
import inspect

logger = logging.getLogger(__name__)

class Tool:
    """
    Universal tool class that can wrap any function.
    Completely dynamic with AI-enhanced capabilities.
    """
    def __init__(self, name: str, description: str, execute_func: Callable,
                 schema: Dict[str, Any] = None, category: str = "general",
                 ai_enhanced: bool = True):
        self.name = name
        self.description = description
        self.execute = execute_func
        self.schema = schema or self._generate_schema()
        self.category = category
        self.ai_enhanced = ai_enhanced

    def _generate_schema(self) -> Dict[str, Any]:
        """Generate schema from function signature."""
        try:
            sig = inspect.signature(self.execute)
            properties = {}
            required = []

            for param_name, param in sig.parameters.items():
                properties[param_name] = {
                    "type": "string",
                    "description": f"Parameter: {param_name}"
                }
                if param.default == inspect.Parameter.empty:
                    required.append(param_name)

            return {
                "type": "object",
                "properties": properties,
                "required": required
            }
        except:
            return {}

    @classmethod
    def from_function(cls, func: Callable, name: str = None,
                     description: str = None, **kwargs) -> 'Tool':
        """Create a Tool from any function."""
        tool_name = name or getattr(func, '__name__', 'unknown_tool')
        tool_desc = description or (func.__doc__ or f"Tool: {tool_name}").strip().split('\n')[0]

        return cls(
            name=tool_name,
            description=tool_desc,
            execute_func=func,
            **kwargs
        )



# Universal Tool Examples - These demonstrate how any function can become a tool

def example_text_processor(text: str, operation: str = "summarize") -> str:
    """
    Example universal tool that processes text.
    This demonstrates how any function can become a tool.
    """
    if operation == "summarize":
        return f"Summary of '{text[:50]}...': This is a simulated summary of the provided text."
    elif operation == "analyze":
        return f"Analysis of '{text[:50]}...': This text contains {len(text)} characters and {len(text.split())} words."
    elif operation == "translate":
        return f"Translation of '{text[:50]}...': [This would be a translation in a real implementation]"
    else:
        return f"Processed '{text[:50]}...' with operation: {operation}"

def example_calculator(expression: str) -> str:
    """
    Example universal calculator tool.
    Shows how any function can be dynamically registered as a tool.
    """
    try:
        # Simple evaluation (in real use, use safer evaluation methods)
        result = eval(expression.replace('^', '**'))
        return f"Result: {expression} = {result}"
    except Exception as e:
        return f"Could not calculate '{expression}': {str(e)}"

def example_data_formatter(data: str, format_type: str = "json") -> str:
    """
    Example data formatting tool.
    Demonstrates universal tool capabilities.
    """
    if format_type == "json":
        try:
            import json
            parsed = json.loads(data)
            return json.dumps(parsed, indent=2)
        except:
            return f"Could not format as JSON: {data}"
    elif format_type == "uppercase":
        return data.upper()
    elif format_type == "lowercase":
        return data.lower()
    else:
        return f"Formatted data ({format_type}): {data}"

# Universal tool creation helpers

def create_tool_from_function(func: Callable, name: str = None,
                             description: str = None, **kwargs) -> Tool:
    """Create a tool from any function with AI-enhanced metadata."""
    return Tool.from_function(func, name, description, **kwargs)

def create_tools_from_module(module_name: str) -> list:
    """Create tools from all functions in a module."""
    import importlib
    tools = []
    try:
        module = importlib.import_module(module_name)
        for name in dir(module):
            if not name.startswith('_'):
                attr = getattr(module, name)
                if callable(attr):
                    tools.append(Tool.from_function(attr, name))
    except Exception as e:
        logger.debug(f"Could not create tools from module {module_name}: {e}")
    return tools

def create_tools_from_object(obj: Any) -> list:
    """Create tools from all methods of an object."""
    tools = []
    for name in dir(obj):
        if not name.startswith('_'):
            attr = getattr(obj, name)
            if callable(attr):
                tool_name = f"{obj.__class__.__name__}.{name}"
                tools.append(Tool.from_function(attr, tool_name))
    return tools

def register_function_as_tool(registry, func: Callable, **kwargs):
    """Register any function as a tool in the registry."""
    tool = Tool.from_function(func, **kwargs)
    registry.register_tool(tool)
    return tool

# Universal tool examples for demonstration
EXAMPLE_TOOLS = [
    example_text_processor,
    example_calculator,
    example_data_formatter
]
