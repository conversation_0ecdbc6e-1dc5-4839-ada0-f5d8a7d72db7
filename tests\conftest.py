"""
Pytest configuration for AI Utility Orchestrator tests.
Simple configuration for the comprehensive test suite.
"""

import pytest
import os


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    original_env = os.environ.copy()

    # Set test environment variables
    os.environ['OPENAI_API_KEY'] = 'test-api-key-for-testing'
    os.environ['AI_ORCHESTRATOR_MOCK_MODE'] = 'true'

    yield

    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)
